#!/usr/bin/env python3
"""
Qwen3-4B 评测配置文件
包含更多样本题目和配置选项
"""

# 评测配置
EVALUATION_CONFIG = {
    "model_name": "Qwen3-4B",
    "max_questions_per_dataset": 10,  # 每个数据集最多测试题目数
    "temperature": 0.1,
    "max_new_tokens": 100,
    "timeout_seconds": 30,
    "retry_attempts": 3
}

# 扩展的测试题目集
EXTENDED_QUESTIONS = {
    "OpenBookQA": [
        {
            "question": "What is the primary source of energy for most life on Earth?",
            "choices": ["A) Wind", "B) The Sun", "C) Geothermal", "D) Nuclear"],
            "answer": "B",
            "type": "science_qa"
        },
        {
            "question": "Which of the following is NOT a renewable energy source?",
            "choices": ["A) Solar", "B) Wind", "C) Coal", "D) Hydroelectric"],
            "answer": "C",
            "type": "science_qa"
        },
        {
            "question": "What happens to water when it freezes?",
            "choices": ["A) It becomes denser", "B) It becomes less dense", "C) Its density stays the same", "D) It disappears"],
            "answer": "B",
            "type": "science_qa"
        },
        {
            "question": "Which gas makes up most of Earth's atmosphere?",
            "choices": ["A) Oxygen", "B) Carbon dioxide", "C) Nitrogen", "D) Hydrogen"],
            "answer": "C",
            "type": "science_qa"
        },
        {
            "question": "What is the smallest unit of matter?",
            "choices": ["A) Molecule", "B) Atom", "C) Cell", "D) Electron"],
            "answer": "B",
            "type": "science_qa"
        }
    ],
    
    "TriviaQA": [
        {
            "question": "What is the capital of Australia?",
            "answer": "Canberra",
            "type": "knowledge_qa"
        },
        {
            "question": "Who wrote the novel '1984'?",
            "answer": "George Orwell",
            "type": "knowledge_qa"
        },
        {
            "question": "What is the largest planet in our solar system?",
            "answer": "Jupiter",
            "type": "knowledge_qa"
        },
        {
            "question": "In which year did the Berlin Wall fall?",
            "answer": "1989",
            "type": "knowledge_qa"
        },
        {
            "question": "Who painted the Mona Lisa?",
            "answer": "Leonardo da Vinci",
            "type": "knowledge_qa"
        }
    ],
    
    "HellaSwag": [
        {
            "context": "A woman is sitting at a piano.",
            "question": "What happens next?",
            "choices": [
                "A) She starts playing a beautiful melody",
                "B) She gets up and walks away",
                "C) She closes the piano lid",
                "D) She adjusts the piano bench"
            ],
            "answer": "A",
            "type": "commonsense"
        },
        {
            "context": "A man is holding an umbrella while it's raining.",
            "question": "What is he most likely to do?",
            "choices": [
                "A) Put the umbrella away",
                "B) Keep walking under the umbrella",
                "C) Start dancing in the rain",
                "D) Throw the umbrella away"
            ],
            "answer": "B",
            "type": "commonsense"
        },
        {
            "context": "A child sees an ice cream truck approaching.",
            "question": "What will the child probably do?",
            "choices": [
                "A) Run away from the truck",
                "B) Ask parents for money to buy ice cream",
                "C) Ignore the truck completely",
                "D) Call the police"
            ],
            "answer": "B",
            "type": "commonsense"
        }
    ],
    
    "SQuAD2": [
        {
            "context": "The Amazon rainforest is located in South America and spans across nine countries. Brazil contains about 60% of the rainforest.",
            "question": "What percentage of the Amazon rainforest is in Brazil?",
            "answer": "60%",
            "type": "reading_comprehension"
        },
        {
            "context": "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
            "question": "What is the population of Mars?",
            "answer": "No answer",
            "type": "reading_comprehension"
        },
        {
            "context": "The Great Wall of China was built over many centuries by different dynasties. It stretches over 13,000 miles.",
            "question": "How long is the Great Wall of China?",
            "answer": "over 13,000 miles",
            "type": "reading_comprehension"
        },
        {
            "context": "Python is a high-level programming language known for its simplicity and readability.",
            "question": "What is the capital of France?",
            "answer": "No answer",
            "type": "reading_comprehension"
        }
    ],
    
    "XWINO": [
        {
            "sentence": "The trophy doesn't fit into the brown suitcase because it is too large.",
            "question": "What does 'it' refer to?",
            "choices": ["A) The trophy", "B) The suitcase"],
            "answer": "A",
            "type": "coreference"
        },
        {
            "sentence": "The city councilmen refused the demonstrators a permit because they feared violence.",
            "question": "Who feared violence?",
            "choices": ["A) The city councilmen", "B) The demonstrators"],
            "answer": "A",
            "type": "coreference"
        },
        {
            "sentence": "The women stopped taking the pills because they were pregnant.",
            "question": "Who was pregnant?",
            "choices": ["A) The women", "B) The pills"],
            "answer": "A",
            "type": "coreference"
        }
    ],
    
    "MMLU": [
        {
            "subject": "High School Mathematics",
            "question": "What is the derivative of x²?",
            "choices": ["A) x", "B) 2x", "C) x²", "D) 2"],
            "answer": "B",
            "type": "academic"
        },
        {
            "subject": "World History", 
            "question": "In which year did World War II end?",
            "choices": ["A) 1944", "B) 1945", "C) 1946", "D) 1947"],
            "answer": "B",
            "type": "academic"
        },
        {
            "subject": "Computer Science",
            "question": "What does CPU stand for?",
            "choices": ["A) Central Processing Unit", "B) Computer Processing Unit", "C) Central Program Unit", "D) Computer Program Unit"],
            "answer": "A",
            "type": "academic"
        },
        {
            "subject": "Biology",
            "question": "What is the powerhouse of the cell?",
            "choices": ["A) Nucleus", "B) Mitochondria", "C) Ribosome", "D) Cytoplasm"],
            "answer": "B",
            "type": "academic"
        }
    ],
    
    "GSM8K": [
        {
            "question": "Sarah has 24 apples. She gives 1/3 of them to her friend and eats 4 apples herself. How many apples does she have left?",
            "answer": "12",
            "solution": "24 ÷ 3 = 8 apples given away. 24 - 8 - 4 = 12 apples left.",
            "type": "math_word_problem"
        },
        {
            "question": "A school has 450 students. If 2/5 are boys, how many girls are there?",
            "answer": "270",
            "solution": "Boys: 450 × 2/5 = 180. Girls: 450 - 180 = 270.",
            "type": "math_word_problem"
        },
        {
            "question": "Tom buys 3 books for $12 each and 2 pens for $3 each. How much does he spend in total?",
            "answer": "42",
            "solution": "Books: 3 × $12 = $36. Pens: 2 × $3 = $6. Total: $36 + $6 = $42.",
            "type": "math_word_problem"
        },
        {
            "question": "A rectangle has a length of 8 cm and a width of 5 cm. What is its area?",
            "answer": "40",
            "solution": "Area = length × width = 8 × 5 = 40 cm².",
            "type": "math_word_problem"
        }
    ],
    
    "MATH": [
        {
            "problem": "Solve for x: 2x + 5 = 13",
            "answer": "4",
            "level": "Algebra",
            "type": "competition_math"
        },
        {
            "problem": "Find the area of a circle with radius 5.",
            "answer": "25π",
            "level": "Geometry", 
            "type": "competition_math"
        },
        {
            "problem": "What is the sum of the first 10 positive integers?",
            "answer": "55",
            "level": "Number Theory",
            "type": "competition_math"
        }
    ],
    
    "BBH": [
        {
            "task": "Logical Deduction",
            "question": "All birds can fly. Penguins are birds. Can penguins fly?",
            "choices": ["A) Yes", "B) No", "C) Cannot determine"],
            "answer": "B",
            "explanation": "The premise is false - not all birds can fly.",
            "type": "reasoning"
        },
        {
            "task": "Causal Judgment",
            "question": "If it rains, the ground gets wet. The ground is wet. Did it rain?",
            "choices": ["A) Yes", "B) No", "C) Cannot determine"],
            "answer": "C",
            "explanation": "The ground could be wet for other reasons.",
            "type": "reasoning"
        }
    ],
    
    "HumanEval": [
        {
            "problem_id": "HumanEval/0",
            "prompt": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\"\n    Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n",
            "canonical_solution": "    for idx, elem in enumerate(numbers):\n        for idx2, elem2 in enumerate(numbers):\n            if idx != idx2:\n                distance = abs(elem - elem2)\n                if distance < threshold:\n                    return True\n    return False",
            "type": "code_generation"
        },
        {
            "problem_id": "HumanEval/1",
            "prompt": "def separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\"\n    Input to this function is a string containing multiple groups of nested parentheses.\n    Your goal is to separate those group into separate strings and return the list of those.\n    Separate groups are balanced (each open brace is properly closed) and not nested within each other.\n    Ignore any spaces in the input string.\n    >>> separate_paren_groups('( ) (( )) (( )( ))')\n    ['()', '(())', '(()())']\n    \"\"\"\n",
            "canonical_solution": "    result = []\n    current_string = []\n    current_depth = 0\n    \n    for c in paren_string:\n        if c == '(':\n            current_depth += 1\n            current_string.append(c)\n        elif c == ')':\n            current_depth -= 1\n            current_string.append(c)\n            \n            if current_depth == 0:\n                result.append(''.join(current_string))\n                current_string = []\n    \n    return result",
            "type": "code_generation"
        }
    ]
}

# 模型部署配置选项
MODEL_CONFIGS = {
    "local_transformers": {
        "model_path": "Qwen/Qwen3-4B-Base",
        "device_map": "auto",
        "torch_dtype": "float16"
    },
    "api_endpoint": {
        "base_url": "http://localhost:8000/v1",
        "api_key": "your-api-key-here"
    },
    "ollama": {
        "model_name": "qwen3:4b",
        "base_url": "http://localhost:11434"
    }
}
