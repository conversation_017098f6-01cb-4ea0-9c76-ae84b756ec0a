#!/usr/bin/env python3
"""
Qwen3-4B 快速测试脚本
用于快速验证评测系统是否正常工作
"""

import sys
import os
from qwen3_4b_evaluation import Qwen3Evaluator
from evaluation_config import EXTENDED_QUESTIONS

def quick_test():
    """快速测试评测系统"""
    print("🚀 Qwen3-4B 快速测试")
    print("=" * 50)
    
    # 创建评估器
    evaluator = Qwen3Evaluator("Qwen3-4B-QuickTest")
    
    # 使用少量题目进行快速测试
    test_questions = {
        "OpenBookQA": EXTENDED_QUESTIONS["OpenBookQA"][:2],
        "MMLU": EXTENDED_QUESTIONS["MMLU"][:2],
        "GSM8K": EXTENDED_QUESTIONS["GSM8K"][:2]
    }
    
    print(f"📝 测试题目总数: {sum(len(q) for q in test_questions.values())}")
    print("🔧 使用模拟回答模式（如需真实模型，请修改配置）")
    print()
    
    # 运行测试
    results = {}
    for dataset_name, questions in test_questions.items():
        print(f"📊 正在测试 {dataset_name}...")
        result = evaluator.evaluate_dataset(dataset_name, questions)
        results[dataset_name] = result
        print(f"   ✅ 完成 - 准确率: {result['accuracy']:.3f}")
    
    # 生成简化报告
    print("\n" + "=" * 50)
    print("📈 测试结果摘要")
    print("=" * 50)
    
    total_questions = sum(r["total_questions"] for r in results.values())
    total_correct = sum(r["correct_answers"] for r in results.values())
    overall_accuracy = total_correct / total_questions if total_questions > 0 else 0
    
    print(f"总题目数: {total_questions}")
    print(f"总正确数: {total_correct}")
    print(f"总体准确率: {overall_accuracy:.3f} ({overall_accuracy*100:.1f}%)")
    print()
    
    print("各数据集详细结果:")
    print("-" * 30)
    for dataset_name, result in results.items():
        accuracy = result["accuracy"]
        print(f"{dataset_name:12} | {result['correct_answers']}/{result['total_questions']} | {accuracy:.3f}")
    
    # 生成完整报告
    markdown_report = evaluator.generate_markdown_report(results)
    
    # 保存报告
    report_filename = "quick_test_report.md"
    with open(report_filename, "w", encoding="utf-8") as f:
        f.write(markdown_report)
    
    print(f"\n📄 详细报告已保存到: {report_filename}")
    print("🎉 快速测试完成！")
    
    return results

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        ("datasets", "数据集加载"),
        ("transformers", "模型推理"),
        ("torch", "深度学习框架")
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def show_sample_questions():
    """显示样例题目"""
    print("\n📝 样例题目预览")
    print("=" * 50)
    
    # 显示每个数据集的第一个题目
    for dataset_name, questions in EXTENDED_QUESTIONS.items():
        if questions:
            question = questions[0]
            print(f"\n【{dataset_name}】")
            
            if "question" in question:
                print(f"问题: {question['question']}")
            elif "problem" in question:
                print(f"问题: {question['problem']}")
            
            if "choices" in question:
                print(f"选项: {', '.join(question['choices'])}")
            
            print(f"答案: {question['answer']}")
            print("-" * 30)

def main():
    """主函数"""
    print("🤖 Qwen3-4B 评测系统")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "check":
            check_dependencies()
            return
        elif command == "sample":
            show_sample_questions()
            return
        elif command == "help":
            print_help()
            return
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 请先安装缺少的依赖包")
        return
    
    print()
    
    # 运行快速测试
    try:
        results = quick_test()
        
        # 询问是否运行完整测试
        print("\n" + "=" * 50)
        response = input("是否运行完整评测？(包含所有数据集) [y/N]: ").strip().lower()
        
        if response in ['y', 'yes']:
            print("\n🚀 开始完整评测...")
            from qwen3_4b_evaluation import main as full_evaluation
            full_evaluation()
        else:
            print("✅ 快速测试完成，如需完整评测请运行: python qwen3_4b_evaluation.py")
            
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        print("请检查配置或联系开发者")

def print_help():
    """显示帮助信息"""
    help_text = """
🤖 Qwen3-4B 评测系统使用说明

用法:
    python quick_test.py [命令]

命令:
    (无参数)    运行快速测试
    check      检查依赖是否安装
    sample     显示样例题目
    help       显示此帮助信息

示例:
    python quick_test.py           # 运行快速测试
    python quick_test.py check     # 检查依赖
    python quick_test.py sample    # 查看样例题目

完整评测:
    python qwen3_4b_evaluation.py  # 运行完整评测

配置文件:
    evaluation_config.py           # 修改测试配置
    README_evaluation.md           # 详细使用说明

输出文件:
    quick_test_report.md           # 快速测试报告
    qwen3_4b_evaluation_report.md  # 完整评测报告
"""
    print(help_text)

if __name__ == "__main__":
    main()
