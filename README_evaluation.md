# Qwen3-4B 综合评测系统

这是一个专门为Qwen3-4B模型设计的综合评测系统，覆盖10个主要的大语言模型评测数据集。

## 功能特点

- ✅ 覆盖10个标准评测数据集
- ✅ 支持多种模型部署方式
- ✅ 自动生成Markdown格式报告
- ✅ 详细的性能分析和可视化
- ✅ 可扩展的题目集和配置

## 支持的数据集

| 数据集 | 类型 | 题目数 | 评测能力 |
|--------|------|--------|----------|
| OpenBookQA | 科学常识 | 5 | 基础科学知识 |
| TriviaQA | 知识问答 | 5 | 广泛知识储备 |
| HellaSwag | 常识推理 | 3 | 日常情境理解 |
| SQuAD2 | 阅读理解 | 4 | 文本理解和信息提取 |
| XWINO | 指代消解 | 3 | 代词指代理解 |
| MMLU | 多学科理解 | 4 | 57个学科综合知识 |
| GSM8K | 小学数学 | 4 | 基础数学应用 |
| MATH | 竞赛数学 | 3 | 高中数学竞赛 |
| BBH | 困难推理 | 2 | 复杂逻辑推理 |
| HumanEval | 代码生成 | 2 | 编程能力 |

## 安装依赖

```bash
# 基础依赖
pip install datasets transformers torch

# 如果使用GPU
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 可选：用于API调用
pip install openai requests

# 可选：用于Ollama
pip install ollama
```

## 使用方法

### 1. 基本使用

```bash
python qwen3_4b_evaluation.py
```

这将运行完整的评测流程并生成报告文件 `qwen3_4b_evaluation_report.md`。

### 2. 配置模型

编辑 `qwen3_4b_evaluation.py` 中的模型调用部分：

#### 方法1: 本地Transformers库

```python
# 默认配置，会自动下载Qwen3-4B模型
# 需要约8GB显存
```

#### 方法2: API调用

```python
def call_qwen_model(self, prompt: str) -> str:
    import requests
    
    response = requests.post(
        "http://your-api-endpoint/v1/chat/completions",
        headers={"Authorization": "Bearer your-api-key"},
        json={
            "model": "qwen3-4b",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 100
        }
    )
    
    return response.json()["choices"][0]["message"]["content"]
```

#### 方法3: Ollama本地部署

```python
def call_qwen_model(self, prompt: str) -> str:
    import ollama
    
    response = ollama.chat(
        model='qwen3:4b',
        messages=[{'role': 'user', 'content': prompt}]
    )
    
    return response['message']['content']
```

### 3. 自定义配置

修改 `evaluation_config.py` 中的配置：

```python
EVALUATION_CONFIG = {
    "model_name": "Qwen3-4B",
    "max_questions_per_dataset": 10,  # 增加题目数量
    "temperature": 0.1,
    "max_new_tokens": 100,
    "timeout_seconds": 30,
    "retry_attempts": 3
}
```

## 输出报告示例

运行完成后会生成详细的Markdown报告，包含：

### 总体表现
- 总题目数: 35
- 总正确数: 22
- 总体准确率: 0.629 (62.9%)

### 各数据集详细结果

| 数据集 | 题目数 | 正确数 | 准确率 | 评测类型 | 说明 |
|--------|--------|--------|--------|----------|------|
| XWINO | 3 | 3 | 1.000 | 指代消解 | 测试代词指代理解 |
| MMLU | 4 | 3 | 0.750 | 多学科理解 | 测试57个学科的综合知识 |
| GSM8K | 4 | 3 | 0.750 | 小学数学 | 测试基础数学应用题 |

### 性能分析
- **优势领域**: 指代消解、多学科理解、数学推理
- **待提升领域**: 常识推理、知识问答、代码生成

### 详细题目分析
每个数据集的具体题目、模型回答和正确性分析。

## 扩展功能

### 1. 添加新题目

在 `evaluation_config.py` 的 `EXTENDED_QUESTIONS` 中添加新题目：

```python
"OpenBookQA": [
    {
        "question": "你的新问题",
        "choices": ["A) 选项1", "B) 选项2", "C) 选项3", "D) 选项4"],
        "answer": "A",
        "type": "science_qa"
    }
]
```

### 2. 添加新数据集

1. 在 `load_sample_questions()` 中添加新数据集
2. 在 `build_prompt()` 中添加对应的提示词模板
3. 在 `check_answer()` 中添加答案检查逻辑

### 3. 批量评测

```python
# 评测多个模型
models = ["Qwen3-4B", "Qwen2.5-7B", "Llama3-8B"]

for model in models:
    evaluator = Qwen3Evaluator(model)
    results = evaluator.run_full_evaluation()
    # 保存结果...
```

## 性能优化建议

### 1. 硬件要求
- **最低配置**: 8GB GPU显存，16GB系统内存
- **推荐配置**: 16GB GPU显存，32GB系统内存
- **CPU**: 支持AVX2指令集

### 2. 加速技巧
- 使用量化模型 (int8/int4)
- 批量处理多个问题
- 使用GPU加速推理

### 3. 内存优化
```python
# 使用量化
model = AutoModelForCausalLM.from_pretrained(
    "Qwen/Qwen3-4B-Base",
    torch_dtype=torch.float16,
    load_in_8bit=True,  # 8位量化
    device_map="auto"
)
```

## 故障排除

### 1. 内存不足
```bash
# 错误: CUDA out of memory
# 解决: 使用CPU或减少batch_size
export CUDA_VISIBLE_DEVICES=""  # 强制使用CPU
```

### 2. 模型下载失败
```bash
# 使用镜像源
export HF_ENDPOINT=https://hf-mirror.com
```

### 3. 依赖冲突
```bash
# 创建虚拟环境
python -m venv qwen_eval
source qwen_eval/bin/activate  # Linux/Mac
# qwen_eval\Scripts\activate  # Windows
pip install -r requirements.txt
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个评测系统：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 参考文献

- [Qwen3 Technical Report](https://arxiv.org/abs/2412.15115)
- [OpenBookQA Dataset](https://allenai.org/data/open-book-qa)
- [MMLU Benchmark](https://arxiv.org/abs/2009.03300)
- [HumanEval Dataset](https://arxiv.org/abs/2107.03374)

---
*最后更新: 2025-08-02*
